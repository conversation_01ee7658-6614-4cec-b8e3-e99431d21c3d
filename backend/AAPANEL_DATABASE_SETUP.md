# AApanel PostgreSQL Database Setup Guide

This guide will help you set up your Crypto Assistant database on AApanel with PostgreSQL.

## 📋 Prerequisites

- AApanel server with PostgreSQL installed
- pgAdmin access to your database
- Database credentials from AApanel

## 🔧 Setup Steps

### 1. Create Database in AApanel

From your screenshot, you've already created:
- **Database name**: CryptoAssistant
- **Username**: CryptoAssistant  
- **Password**: IvLfcLJ5MCCLnZ

### 2. Run Database Schema

1. Open pgAdmin and connect to your AApanel PostgreSQL server
2. Navigate to your `CryptoAssistant` database
3. Open the Query Tool
4. Copy and paste the contents of `database_schema.sql`
5. Execute the script to create all tables and indexes

### 3. Update Environment Variables

1. Find your AApanel server IP address
2. Update the `DATABASE_URL` in your `.env` file:
   ```env
   DATABASE_URL="***************************************************************/CryptoAssistant"
   ```
   Replace `YOUR_SERVER_IP` with your actual server IP.

### 4. Test Database Connection

Run the test script to verify everything is working:
```bash
cd backend
node test-db-connection.js
```

### 5. Update Production Deployment

If deploying to Render or other platforms, update your environment variables there as well.

## 🗄️ Database Schema Overview

The schema includes 4 main tables:

### admin_settings
- Stores configurable application settings
- Confidence thresholds, timeframes, etc.

### notification_rules  
- Defines rules for when to trigger notifications
- Confidence/strength thresholds, timeframes, signal types

### signal_history
- Stores all generated trading signals
- Technical indicators, patterns, confidence scores

### notifications
- Stores triggered notifications
- Links to rules and signals that triggered them

## 🔍 Troubleshooting

### Connection Issues
1. **Server not accessible**: Check if PostgreSQL port (5432) is open in AApanel firewall
2. **Authentication failed**: Verify username/password in AApanel database settings
3. **Database not found**: Ensure the database "CryptoAssistant" exists
4. **Network timeout**: Check server IP address and network connectivity

### Common Solutions
- Ensure PostgreSQL service is running in AApanel
- Check if remote connections are allowed in PostgreSQL config
- Verify the database user has proper permissions
- Test connection from pgAdmin first before testing from application

## 📊 Default Settings

The schema includes default admin settings:
- Confidence threshold: 70%
- Strength threshold: 60%  
- Active timeframes: ["1m","15m","30m","4h"]
- Notifications: enabled

## 🚀 Next Steps

After successful setup:
1. Start your backend server: `npm run dev`
2. Check logs for successful database connection
3. Test the admin panel at `http://localhost:5001/api/admin`
4. Configure notification rules as needed

## 📝 Notes

- No data migration needed - fresh database setup
- All existing Prisma functionality will work unchanged
- WebSocket and real-time features remain intact
- Admin portal and notification system ready to use
