Run the commands: 

sudo apt update
sudo apt install postgresql-client -y


Run this command then: 

sudo nano /www/server/pgsql/data/postgresql.conf


Look for the line:

#listen_addresses = 'localhost'


Change it to:

listen_addresses = '*' (REMOVE THE # as well don't forget)

then save and exit (Ctrl+O, <PERSON>ter, Ctrl+X).


Now edit the host-based authentication file:


sudo nano /www/server/pgsql/data/pg_hba.conf


Scroll to the bottom and add this line:

host    all             all             0.0.0.0/0               md5


Then press control O, enter and Control X

then run: 

sudo -u postgres /www/server/pgsql/bin/pg_ctl restart -D /www/server/pgsql/data/


Then verify it with: 


sudo netstat -tlnp | grep 5432


if you see a line or two with 5432 in it, you're good to go!



