Run the commands: 

sudo apt update
sudo apt install postgresql-client -y


Run this command then: 

sudo nano /www/server/pgsql/data/postgresql.conf


Look for the line:

#listen_addresses = 'localhost'


Change it to:

listen_addresses = '*'

then save and exit (Ctrl+O, Enter, Ctrl+X).


Now edit the host-based authentication file:


sudo nano /www/server/pgsql/data/pg_hba.conf


Scroll to the bottom and add this line:

host    all             all             0.0.0.0/0               md5


Then press control O, enter and Control X

then run: 

sudo /etc/init.d/pgsql restart
