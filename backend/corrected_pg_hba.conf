# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     trust
# IPv4 local connections:
host    all             all             127.0.0.1/32            trust
# IPv6 local connections:
host    all             all             ::1/128                 trust
# Allow replication connections from localhost, by a user with the
# replication privilege.
local   replication     all                                     trust
host    replication     all             127.0.0.1/32            trust
host    replication     all             ::1/128                 trust

# Remote connections for specific databases
host    newdatabase     newdatabase     0.0.0.0/0               md5
host    CryptoAssistant CryptoAssistant 0.0.0.0/0               md5

# Allow all remote connections (fallback)
host    all             all             0.0.0.0/0               md5
