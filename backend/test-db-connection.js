#!/usr/bin/env node

/**
 * Database Connection Test Script
 * Run this script to test your AApanel PostgreSQL connection
 * 
 * Usage: node test-db-connection.js
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

async function testDatabaseConnection() {
    console.log('🔍 Testing database connection...');
    console.log(`📍 Database URL: ${process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':****@')}`);
    
    const prisma = new PrismaClient({
        log: ['error', 'warn'],
    });

    try {
        // Test basic connection
        console.log('⏳ Connecting to database...');
        await prisma.$connect();
        console.log('✅ Database connection successful!');

        // Test if tables exist
        console.log('⏳ Checking if tables exist...');
        
        const adminSettingsCount = await prisma.adminSettings.count();
        console.log(`✅ admin_settings table exists (${adminSettingsCount} records)`);
        
        const notificationRulesCount = await prisma.notificationRule.count();
        console.log(`✅ notification_rules table exists (${notificationRulesCount} records)`);
        
        const signalHistoryCount = await prisma.signalHistory.count();
        console.log(`✅ signal_history table exists (${signalHistoryCount} records)`);
        
        const notificationsCount = await prisma.notification.count();
        console.log(`✅ notifications table exists (${notificationsCount} records)`);

        // Test basic CRUD operations
        console.log('⏳ Testing basic operations...');
        
        // Try to create a test admin setting
        const testSetting = await prisma.adminSettings.upsert({
            where: { settingKey: 'test_connection' },
            update: { 
                settingValue: new Date().toISOString(),
                updatedAt: new Date()
            },
            create: {
                settingKey: 'test_connection',
                settingValue: new Date().toISOString(),
                settingType: 'string',
                description: 'Test connection setting',
                category: 'test'
            }
        });
        console.log('✅ Create/Update operation successful');

        // Try to read the setting
        const readSetting = await prisma.adminSettings.findUnique({
            where: { settingKey: 'test_connection' }
        });
        console.log('✅ Read operation successful');

        // Clean up test data
        await prisma.adminSettings.delete({
            where: { settingKey: 'test_connection' }
        });
        console.log('✅ Delete operation successful');

        console.log('\n🎉 All database tests passed! Your AApanel PostgreSQL database is ready to use.');
        
    } catch (error) {
        console.error('❌ Database connection failed:');
        console.error('Error details:', error.message);
        
        if (error.code) {
            console.error('Error code:', error.code);
        }
        
        console.log('\n🔧 Troubleshooting tips:');
        console.log('1. Make sure your AApanel PostgreSQL server is running');
        console.log('2. Verify the server IP address in your DATABASE_URL');
        console.log('3. Check that port 5432 is accessible from your machine');
        console.log('4. Ensure the database "CryptoAssistant" exists');
        console.log('5. Verify the username and password are correct');
        console.log('6. Run the database_schema.sql script in pgAdmin first');
        
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// Run the test
testDatabaseConnection();
